import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import React, { useRef, useState } from 'react';
import {
    Alert,
    FlatList,
    Keyboard,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { IconSymbol } from './ui/IconSymbol';

// Simple status enum for tasks
enum Status {
  TODO = 'todo',
  COMPLETED = 'completed'
}

type TodoItem = {
  id: string;
  text: string;
  status: Status;
  createdAt: Date;
  isEditing?: boolean;
};

type TodoListProps = {
  title: string;
};

export function TodoList({ title }: TodoListProps) {
  // State for todo list
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [newTodo, setNewTodo] = useState('');
  const [editText, setEditText] = useState('');

  // Get theme colors
  const colorScheme = useColorScheme() ?? 'light';
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = Colors[colorScheme].tint;

  // Define theme-aware colors
  const isDark = colorScheme === 'dark';
  const cardBgColor = isDark ? 'rgba(30, 30, 30, 0.8)' : 'rgba(255, 255, 255, 0.8)';
  const completedCardBgColor = isDark ? 'rgba(40, 40, 40, 0.5)' : 'rgba(0, 0, 0, 0.03)';
  const instructionsBgColor = isDark ? 'rgba(50, 50, 50, 0.5)' : 'rgba(0, 0, 0, 0.03)';
  const borderColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
  const statBadgeBgColor = isDark ? 'rgba(10, 126, 164, 0.2)' : 'rgba(33, 150, 243, 0.1)';
  const clearBtnBgColor = isDark ? 'rgba(255, 82, 82, 0.2)' : 'rgba(255, 82, 82, 0.1)';
  const placeholderColor = isDark ? '#888' : '#aaa';

  const inputRef = useRef<TextInput>(null);

  // CREATE: Add a new todo item
  const addTodo = () => {
    if (newTodo.trim() === '') {
      Alert.alert('Task Required', 'Please enter a task before adding');
      return;
    }

    const newItem: TodoItem = {
      id: Date.now().toString(),
      text: newTodo,
      status: Status.TODO,
      createdAt: new Date(),
    };

    setTodos([...todos, newItem]);
    setNewTodo('');
    Keyboard.dismiss();
  };

  // READ: Get all todos (already handled by the todos state)

  // UPDATE: Toggle todo status (mark as complete/incomplete)
  const toggleTodoStatus = (id: string) => {
    setTodos(
      todos.map(todo =>
        todo.id === id ? {
          ...todo,
          status: todo.status === Status.TODO ? Status.COMPLETED : Status.TODO
        } : todo
      )
    );
  };

  // UPDATE: Start editing a todo
  const startEditing = (id: string) => {
    const todoToEdit = todos.find(todo => todo.id === id);
    if (todoToEdit) {
      setEditText(todoToEdit.text);
      setTodos(
        todos.map(todo =>
          todo.id === id ? { ...todo, isEditing: true } : { ...todo, isEditing: false }
        )
      );
    }
  };

  // UPDATE: Save edited todo
  const saveEdit = (id: string) => {
    if (editText.trim() === '') {
      Alert.alert('Task Required', 'Task cannot be empty');
      return;
    }

    setTodos(
      todos.map(todo =>
        todo.id === id ? { ...todo, text: editText, isEditing: false } : todo
      )
    );
    setEditText('');
    Keyboard.dismiss();
  };

  // DELETE: Remove a todo
  const deleteTodo = (id: string) => {
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          onPress: () => setTodos(todos.filter(todo => todo.id !== id)),
          style: 'destructive'
        }
      ]
    );
  };

  // DELETE ALL: Clear all completed todos
  const clearCompleted = () => {
    if (todos.some(todo => todo.status === Status.COMPLETED)) {
      Alert.alert(
        'Clear Completed',
        'Remove all completed tasks?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Clear',
            onPress: () => setTodos(todos.filter(todo => todo.status === Status.TODO)),
            style: 'destructive'
          }
        ]
      );
    }
  };

  // Stats
  const completedCount = todos.filter(todo => todo.status === Status.COMPLETED).length;
  const activeCount = todos.length - completedCount;

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 140} // Increased offset to account for tab bar
    >
      {/* Header with title and stats */}
      <ThemedView style={styles.header}>
        <ThemedText type="title">{title}</ThemedText>

        <ThemedView style={styles.statsContainer}>
          <ThemedView style={[styles.statBadge, { backgroundColor: statBadgeBgColor }]}>
            <ThemedText style={styles.statText}>
              {activeCount} to do
            </ThemedText>
          </ThemedView>

          <ThemedView style={[styles.statBadge, { backgroundColor: statBadgeBgColor }]}>
            <ThemedText style={styles.statText}>
              {completedCount} completed
            </ThemedText>
          </ThemedView>

          {completedCount > 0 && (
            <TouchableOpacity
              style={[styles.clearButton, { backgroundColor: clearBtnBgColor }]}
              onPress={clearCompleted}
            >
              <ThemedText style={styles.clearButtonText}>
                Clear
              </ThemedText>
            </TouchableOpacity>
          )}
        </ThemedView>
      </ThemedView>

      {/* Add new todo input */}
      <ThemedView style={styles.inputContainer}>
        <View style={[styles.inputWrapper, { borderColor: borderColor }]}>
          <IconSymbol
            name="plus.circle.fill"
            size={24}
            color={tintColor}
            style={styles.inputIcon}
          />
          <TextInput
            ref={inputRef}
            style={[styles.input, { color: textColor }]}
            value={newTodo}
            onChangeText={setNewTodo}
            placeholder="Add a new task..."
            placeholderTextColor={placeholderColor}
            onSubmitEditing={addTodo}
            returnKeyType="done"
          />
        </View>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: tintColor }]}
          onPress={addTodo}
        >
          <ThemedText style={styles.addButtonText}>Add</ThemedText>
        </TouchableOpacity>
      </ThemedView>

      {/* Instructions */}
      <ThemedView style={[styles.instructionsContainer, { backgroundColor: instructionsBgColor }]}>
        <ThemedText style={styles.instructionsText}>
          • Tap a task to mark it as complete
        </ThemedText>
        <ThemedText style={styles.instructionsText}>
          • Long press to edit a task
        </ThemedText>
        <ThemedText style={styles.instructionsText}>
          • Swipe left to delete a task
        </ThemedText>
      </ThemedView>

      {/* Todo list */}
      {todos.length > 0 ? (
        <FlatList
          data={todos}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          renderItem={({ item }) => (
            <ThemedView style={[
              styles.todoCard,
              { backgroundColor: cardBgColor },
              item.status === Status.COMPLETED && [styles.completedCard, { backgroundColor: completedCardBgColor }]
            ]}>
              {item.isEditing ? (
                // Edit mode
                <View style={styles.editContainer}>
                  <TextInput
                    style={[styles.editInput, { color: textColor, borderBottomColor: borderColor }]}
                    value={editText}
                    onChangeText={setEditText}
                    autoFocus
                    onSubmitEditing={() => saveEdit(item.id)}
                    placeholderTextColor={placeholderColor}
                  />
                  <View style={styles.editActions}>
                    <TouchableOpacity
                      style={[styles.editButton, { backgroundColor: tintColor }]}
                      onPress={() => saveEdit(item.id)}
                    >
                      <IconSymbol name="checkmark" size={20} color="#fff" />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.cancelButton, { backgroundColor: clearBtnBgColor }]}
                      onPress={() => setTodos(
                        todos.map(todo =>
                          todo.id === item.id ? { ...todo, isEditing: false } : todo
                        )
                      )}
                    >
                      <IconSymbol name="xmark" size={20} color="#FF5252" />
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                // View mode
                <>
                  <TouchableOpacity
                    style={[
                      styles.checkbox,
                      item.status === Status.COMPLETED && styles.checkedBox,
                      { borderColor: tintColor }
                    ]}
                    onPress={() => toggleTodoStatus(item.id)}
                  >
                    {item.status === Status.COMPLETED && (
                      <IconSymbol name="checkmark" size={16} color="#fff" />
                    )}
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.todoTextContainer}
                    onLongPress={() => startEditing(item.id)}
                    delayLongPress={500}
                  >
                    <ThemedText
                      style={[
                        styles.todoText,
                        item.status === Status.COMPLETED && styles.completedText
                      ]}
                    >
                      {item.text}
                    </ThemedText>

                    <ThemedText style={styles.dateText}>
                      {item.createdAt.toLocaleDateString()}
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => deleteTodo(item.id)}
                  >
                    <IconSymbol name="trash" size={20} color="#FF5252" />
                  </TouchableOpacity>
                </>
              )}
            </ThemedView>
          )}
        />
      ) : (
        // Empty state
        <ThemedView style={styles.emptyContainer}>
          <IconSymbol
            name="list.bullet.clipboard"
            size={80}
            color={tintColor}
            style={{ opacity: isDark ? 0.7 : 0.5 }}
          />
          <ThemedText style={[styles.emptyText, { opacity: isDark ? 0.8 : 0.6 }]}>
            Your to-do list is empty. Add a task to get started!
          </ThemedText>
        </ThemedView>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    paddingBottom: 24, // Extra bottom padding
  },
  header: {
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    marginTop: 8,
    alignItems: 'center',
  },
  statBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  statText: {
    fontSize: 14,
    fontWeight: '600',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  clearButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FF5252',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 12,
    marginRight: 8,
    height: 50,
  },
  inputIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    height: 50,
  },
  addButton: {
    paddingHorizontal: 16,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  instructionsContainer: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },
  instructionsText: {
    fontSize: 14,
    marginBottom: 4,
  },
  listContent: {
    paddingBottom: 150, // Increased padding to account for tab bar
  },
  todoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  completedCard: {
    // Style applied in addition to todoCard for completed items
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkedBox: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  todoTextContainer: {
    flex: 1,
  },
  todoText: {
    fontSize: 16,
    marginBottom: 4,
  },
  dateText: {
    fontSize: 12,
    opacity: 0.6,
  },
  completedText: {
    textDecorationLine: 'line-through',
    opacity: 0.6,
  },
  deleteButton: {
    padding: 8,
  },
  editContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  editInput: {
    flex: 1,
    borderBottomWidth: 1,
    fontSize: 16,
    marginRight: 8,
    paddingVertical: 4,
  },
  editActions: {
    flexDirection: 'row',
  },
  editButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  cancelButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    maxWidth: '80%',
  },
});
