import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import { StyleSheet, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { TodoList } from '@/components/TodoList';

export default function HomeScreen() {
  const insets = useSafeAreaInsets();
  const backgroundColor = useThemeColor({}, 'background');
  const colorScheme = useColorScheme() ?? 'light';

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor },
        // Don't add bottom padding here since we handle it in the tab bar
        { paddingBottom: 0 }
      ]}
      edges={['top', 'left', 'right']} // Exclude bottom edge
    >
      <TodoList title="Todo List 1" />

      {/* Add extra space at the bottom for the tab bar */}
      <View style={{ height: 70 }} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
